import firestore from '@react-native-firebase/firestore';
import {IComment} from '../interfaces/ITask';

export const addCommentToTask = async (
  taskId: string,
  comment: IComment,
): Promise<boolean> => {
  try {
    // Add comment to subcollection
    await firestore()
      .collection('Tasks')
      .doc(taskId)
      .collection('comments')
      .add(comment);

    // Update task's updatedAt timestamp
    await firestore().collection('Tasks').doc(taskId).update({
      updatedAt: firestore.FieldValue.serverTimestamp(),
    });

    return true;
  } catch (error) {
    console.error('Error adding comment to task:', error);
    return false;
  }
};

export const subscribeToTaskComments = (
  taskId: string,
  callback: (comments: IComment[]) => void,
) => {
  return firestore()
    .collection('Tasks')
    .doc(taskId)
    .collection('comments')
    .orderBy('createdAt', 'desc')
    .onSnapshot(
      snapshot => {
        const comments: IComment[] = [];
        snapshot.forEach(doc => {
          const data = doc.data();
          const comment: IComment = {
            id: doc.id,
            message: data.message,
            senderId: data.senderId,
            createdAt: data.createdAt,
          };
          comments.push(comment);
        });
        callback(comments);
      },
      error => {
        console.error('Error subscribing to task comments:', error);
      },
    );
};
