import * as React from 'react';
import {createNativeStackNavigator} from '@react-navigation/native-stack';
import Home from '../screens/appflow/Home/Home';
import ProgressDetails from '../screens/appflow/ProgressDetails/ProgressDetails';
import {TouchableOpacity, Image} from 'react-native';
import {appStyles, Colors, Fonts} from '../utilities/theme/theme';
import {images} from '../assets/images';
import EditProfile from '../screens/appflow/EditProfile/EditProfile';
import Notification from '../screens/appflow/Notification/Notification';
import ChangePassword from '../screens/appflow/ChangePassword/ChangePassword';
import UpdatedPassword from '../screens/appflow/UpdatedPassword/UpdatedPassword';
import Policy from '../screens/appflow/Policy/Policy';
import CompleteTask from '../screens/appflow/CompleteTasks/CompleteTasks';
import OngoingTask from '../screens/appflow/OngoingTasks/OngoingTasks';
import PdfScreen from '../screens/appflow/PdfScreen/PdfScreen';
import IssueDetails from '../screens/appflow/IssueDetails.tsx/IssueDetails';
import {categoryStructure, IIssue} from '../interfaces/IIssue';
import ClientBottomTabs from './ClientBottomTabs';
import {AuthContext} from '../../App';
import ManagerTodoIssue from '../screens/appflow/ManagerTodoIssue/ManagerTodoIssue';
import IssueTasks from '../screens/appflow/IssueTasks/IssueTasks';
import ManagerBottomTabs from './ManagerBottomTabs';
import TaskDetails from '../screens/appflow/TaskDetails/TaskDetails';
import CreateTask from '../screens/appflow/CreateTask/CreateTask';
import {ITask} from '../interfaces/ITask';
import {IUser} from '../interfaces/IUser';
import ManagerIssuesListing from '../screens/appflow/ManagerIssuesListing/ManagerIssuesListing';

export type HomeStackParamsList = {
  BottomTabs: undefined;
  ClientDashboard: undefined;
  TaskDetails: {taskDetails: ITask; staffUser?: IUser};
  Home: undefined;
  ProgressDetails: {userData: IUser; tasks: ITask[]};
  TeamsProgress: undefined;
  EditProfile: undefined;
  Notification: undefined;
  ChangePassword: undefined;
  UpdatedPassword: undefined;
  Policy: undefined;
  OngoingTask: undefined;
  CompleteTask: undefined;
  Profile: undefined;
  PdfScreen: {attachmentUrl: string};
  IssueDetails: {issue: IIssue};
  SelectAttachment: {selectedCatIds: string[]; category: categoryStructure};
  SelectCategory: undefined;
  ManagerDashBoard: undefined;
  ManagerIssuesListing: {issueStatus: string};
  ManagerTodoIssue: undefined;
  IssueTasks: {issue: IIssue};
  CreateTask: {
    issueId: string;
    isFromIssueTaskScreen?: boolean;
  };
};

const Stack = createNativeStackNavigator<HomeStackParamsList>();

const HomeStackNavigation = () => {
  const {userData} = React.useContext(AuthContext);

  return (
    <Stack.Navigator
      screenOptions={({navigation}) => ({
        headerShown: true,
        headerTitleAlign: 'center',
        headerShadowVisible: false,
        headerTitleStyle: appStyles.headerTitleStyle,
        headerStyle: appStyles.headerStyle,
        headerLeft: () => (
          <TouchableOpacity onPress={() => navigation.goBack()}>
            <Image
              style={{height: 22, width: 22, marginLeft: 8}}
              source={images.leftarrow}
              resizeMode="contain"
            />
          </TouchableOpacity>
        ),
        navigationBarColor: Colors.white,
      })}>
      <Stack.Screen
        name="BottomTabs"
        component={
          userData.userType === 'client'
            ? ClientBottomTabs
            : userData.userType === 'manager'
            ? ManagerBottomTabs
            : ClientBottomTabs
        }
        options={{headerShown: false}}
      />
      <Stack.Screen
        name="Home"
        component={Home}
        options={{headerShown: false}}
      />

      <Stack.Screen
        name="TaskDetails"
        component={TaskDetails}
        options={{headerShown: true, headerTitle: 'Task Details'}}
      />

      <Stack.Screen
        name="ProgressDetails"
        component={ProgressDetails}
        options={{headerShown: true, headerTitle: ''}}
      />

      <Stack.Screen
        name="EditProfile"
        component={EditProfile}
        options={{headerShown: true, headerTitle: ''}}
      />

      <Stack.Screen
        name="Notification"
        component={Notification}
        options={{
          headerShown: true,
          headerTitle: 'Notifications',
          headerTitleStyle: {
            color: Colors.primary,
            fontSize: 14,
            fontFamily: Fonts.Semibold,
          },
        }}
      />

      <Stack.Screen
        name="ChangePassword"
        component={ChangePassword}
        options={{headerShown: true, headerTitle: ''}}
      />

      <Stack.Screen
        name="UpdatedPassword"
        component={UpdatedPassword}
        options={{headerShown: false, headerTitle: ''}}
      />

      <Stack.Screen
        name="Policy"
        component={Policy}
        options={{headerShown: true, headerTitle: 'Privacy Policy'}}
      />

      <Stack.Screen
        name="OngoingTask"
        component={OngoingTask}
        options={{
          headerShown: true,
          headerTitle: 'Ongoing Tasks',
        }}
      />

      <Stack.Screen
        name="CompleteTask"
        component={CompleteTask}
        options={{
          headerShown: true,
          headerTitle: 'Complete Tasks',
        }}
      />

      <Stack.Screen
        name="PdfScreen"
        component={PdfScreen}
        options={{
          headerShown: true,
          headerTitle: '',
        }}
      />
      <Stack.Screen
        name="IssueDetails"
        component={IssueDetails}
        options={{
          headerShown: true,
          headerTitle: 'Details',
        }}
      />
      <Stack.Screen
        name="ManagerIssuesListing"
        component={ManagerIssuesListing}
        options={{headerShown: true}}
      />
      <Stack.Screen
        name="ManagerTodoIssue"
        component={ManagerTodoIssue}
        options={{headerShown: true, headerTitle: 'To Do'}}
      />
      <Stack.Screen
        name="IssueTasks"
        component={IssueTasks}
        options={{headerShown: true, headerTitle: 'Ticket Details'}}
      />
      <Stack.Screen
        name="CreateTask"
        component={CreateTask}
        options={{headerShown: true, headerTitle: 'Create New Task'}}
      />
    </Stack.Navigator>
  );
};

export default HomeStackNavigation;
