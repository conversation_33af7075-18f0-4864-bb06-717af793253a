import {StyleSheet, Text, View, Image} from 'react-native';
import React from 'react';
import {IComment} from '../../interfaces/ITask';
import {Colors, Fonts} from '../../utilities/theme/theme';
import moment from 'moment';
import {IUser} from '../../interfaces/IUser';
import HorizontalLine from '../common/HorizontalLine/HorizontalLine';
import {images} from '../../assets/images';
import {Timestamp} from '@react-native-firebase/firestore';

interface Props {
  comment: IComment;
  senderData: IUser;
}

const CommentMessages: React.FC<Props> = ({comment, senderData}) => {
  const isManager = senderData.userType === 'manager';

  return (
    <View style={{paddingHorizontal: 16}}>
      <View style={styles.container}>
        <Image
          source={{
            uri:
              senderData.profileImage?.url || String(images.avatarPlaceholder),
          }}
          style={styles.avatar}
        />
        <View style={styles.messageContainer}>
          <View style={styles.header}>
            <View style={styles.senderInfo}>
              <Text style={styles.senderName}>{senderData.name}</Text>
              <Text style={styles.timestamp}>
                {comment?.createdAt instanceof Timestamp
                  ? moment(comment.createdAt.toDate()).format(
                      'DD MMM YYYY h:mm A',
                    )
                  : ''}
              </Text>
            </View>
            <Text style={styles.senderRole}>
              {isManager ? 'Manager' : 'Staff'}
            </Text>
          </View>
          <Text style={styles.message}>{comment.message}</Text>
        </View>
      </View>
      <HorizontalLine backgroundColor={Colors.grey2} marginVertical={16} />
    </View>
  );
};

export default CommentMessages;

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
  },
  avatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: 12,
  },
  messageContainer: {
    flex: 1,
  },
  header: {
    marginBottom: 16,
  },
  senderName: {
    fontSize: 14,
    fontFamily: Fonts.Semibold,
    color: Colors.primary_text,
  },
  senderRole: {
    fontSize: 12,
    fontFamily: Fonts.Medium,
    color: Colors.buttonbgcolor,
    lineHeight: 15,
  },
  timestamp: {
    fontSize: 10,
    fontFamily: Fonts.Regular,
    color: Colors.grey1,
  },
  message: {
    fontSize: 14,
    fontFamily: Fonts.Regular,
    color: '#475467',
    lineHeight: 18,
  },
  senderInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    flex: 1,
  },
});
