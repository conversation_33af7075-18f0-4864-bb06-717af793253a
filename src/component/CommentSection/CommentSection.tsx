import React, {useEffect, useState} from 'react';
import {StyleSheet, Text, View, ActivityIndicator} from 'react-native';
import {IComment, ITask} from '../../interfaces/ITask';
import CommentMessages from './CommentMessages';
import {Colors, Fonts} from '../../utilities/theme/theme';
import {getUserById} from '../../helpers/getUserById';
import {IUser} from '../../interfaces/IUser';
import {subscribeToTaskComments} from '../../backend/comments';

interface Props {
  taskDetails: ITask;
}

const CommentSection: React.FC<Props> = ({taskDetails}) => {
  const [comments, setComments] = useState<IComment[]>([]);
  const [loading, setLoading] = useState(true);
  const [userCache, setUserCache] = useState<Record<string, IUser>>({});
  const taskId = taskDetails.id;

  // Subscribe to real-time comments when component mounts
  useEffect(() => {
    const unsubscribe = subscribeToTaskComments(taskId, newComments => {
      setComments(newComments);
    });

    // Cleanup subscription on unmount
    return () => {
      if (unsubscribe) {
        unsubscribe();
      }
    };
  }, [taskId]);

  // Fetch user data for each comment sender
  useEffect(() => {
    const fetchUsers = async () => {
      try {
        const userIds = new Set<string>();

        // Add manager and staff IDs
        if (taskDetails.managerId) userIds.add(taskDetails.managerId);
        if (taskDetails.staffId) userIds.add(taskDetails.staffId);

        // Add comment sender IDs
        comments.forEach(comment => {
          if (comment.senderId) userIds.add(comment.senderId);
        });

        // Fetch user data for each unique ID
        const newUserCache = {...userCache};

        for (const id of Array.from(userIds)) {
          if (!newUserCache[id]) {
            const user = await getUserById(id);
            if (user) {
              newUserCache[id] = user;
            }
          }
        }

        setUserCache(newUserCache);
      } catch (error) {
        console.error('Error fetching user data:', error);
      } finally {
        setLoading(false);
      }
    };

    if (comments.length > 0) {
      fetchUsers();
    }
  }, [comments, taskDetails]);

  const getSenderData = (senderId: string): IUser => {
    return (
      userCache[senderId] || {
        id: senderId,
        name: '',
        email: '',
        userType: '',
      }
    );
  };

  return (
    <View style={styles.container}>
      {loading ? null : (
        <>
          <Text style={styles.sectionTitle}>Activity</Text>
          <View style={styles.commentsContainer}>
            {comments.map(comment => (
              <CommentMessages
                key={comment.id}
                comment={comment}
                senderData={getSenderData(comment.senderId)}
              />
            ))}
          </View>
        </>
      )}
    </View>
  );
};

export default CommentSection;

const styles = StyleSheet.create({
  container: {
    backgroundColor: Colors.white,
    borderBottomLeftRadius: 16,
    borderBottomRightRadius: 16,
    paddingBottom: 16,
    paddingHorizontal: 16,
  },
  sectionTitle: {
    fontSize: 12,
    fontFamily: Fonts.Semibold,
    color: Colors.primary_text,
    marginBottom: 16,
    paddingHorizontal: 16,
  },
  commentsContainer: {
    marginBottom: 16,
  },
  loader: {
    marginVertical: 20,
  },
  emptyText: {
    textAlign: 'center',
    fontFamily: Fonts.Medium,
    color: Colors.placeholder,
    marginVertical: 20,
    paddingHorizontal: 16,
  },
});
