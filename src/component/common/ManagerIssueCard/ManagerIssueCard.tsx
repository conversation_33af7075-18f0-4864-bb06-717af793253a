import React, {useEffect, useMemo, useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  ViewStyle,
  TouchableOpacity,
  ActivityIndicator,
  Image,
} from 'react-native';
import ProgressBar from '../../common/ProgressBar/ProgressBar';
import {Colors, Fonts} from '../../../utilities/theme/theme';
import {getColorByStatus} from '../../../helpers/getColorByStatus';
import {firebase, Timestamp} from '@react-native-firebase/firestore';
import {getUserById} from '../../../helpers/getUserById';
import {IUser} from '../../../interfaces/IUser';
import moment from 'moment';
import {images} from '../../../assets/images';
import {ITask} from '../../../interfaces/ITask';
import {ArrowRight} from '../../../assets/svgIcons';

// Props interface for the card
interface ManagerIssueCardProps {
  timeLeft: string;
  priority: string;
  status: string;
  title: string;
  note: string;
  dueDate: Timestamp;
  containerStyle?: ViewStyle;
  assignedStaff?: string[];
  onViewIssueDetails: () => void;
  issueId: string;
}

const ManagerIssueCard: React.FC<ManagerIssueCardProps> = ({
  timeLeft,
  priority,
  status,
  title,
  note,
  dueDate,
  containerStyle,
  assignedStaff,
  onViewIssueDetails,
  issueId,
}) => {
  const [avatars, setAvatars] = useState<string[]>([]);
  const [tasks, setTasks] = useState<ITask[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchAvatars = async () => {
      if (assignedStaff && assignedStaff.length > 0) {
        const avatarUrls: string[] = await Promise.all(
          assignedStaff.map(async (id: string): Promise<string> => {
            const user: IUser | null = await getUserById(id);
            return user?.profileImage?.url ?? '';
          }),
        );
        setAvatars(avatarUrls.filter(Boolean));
      } else {
        setAvatars([]);
      }
    };
    fetchAvatars();
  }, [assignedStaff]);

  useEffect(() => {
    if (!issueId) return;
    const unsubscribe = firebase
      .firestore()
      .collection('Tasks')
      .where('issueId', '==', issueId)
      .onSnapshot(
        snapshot => {
          const updatedTasks = snapshot.docs.map(
            doc =>
              ({
                id: doc.id,
                ...doc.data(),
              } as ITask),
          );
          setTasks(updatedTasks);
          setLoading(false);
        },
        error => {
          console.error('Error fetching tasks:', error);
          setLoading(false);
        },
      );

    return () => unsubscribe(); // Clean up on unmount
  }, [issueId]);

  const completeTasks = useMemo(
    () => tasks.filter(task => task.status === 'completed'),
    [tasks],
  );

  return (
    <TouchableOpacity
      onPress={onViewIssueDetails}
      style={[styles.card, containerStyle]}>
      {/* Labels Row: Time Left, Priority, Status */}
      <View style={{flexDirection: 'row', justifyContent: 'space-between'}}>
        <View style={styles.labelsRow}>
          {timeLeft && (
            <View style={[styles.label, {backgroundColor: '#D21F3C'}]}>
              <Text style={[styles.labelText, {color: '#fff'}]}>
                {timeLeft}
              </Text>
            </View>
          )}
          {priority && (
            <View style={[styles.label, {backgroundColor: '#19211E'}]}>
              <Text style={[styles.labelText, {color: '#fff'}]}>
                {priority}
              </Text>
            </View>
          )}
          <View
            style={[
              styles.label,
              {backgroundColor: getColorByStatus(status as any) as any},
            ]}>
            <Text style={[styles.labelText, {color: Colors.white}]}>
              {status}
            </Text>
          </View>
        </View>
        <TouchableOpacity
          hitSlop={16}
          style={styles.detailsBtn}
          onPress={onViewIssueDetails}>
          <Text style={styles.detailsText}>View Details</Text>
          <ArrowRight width={16} height={10} />
        </TouchableOpacity>
      </View>
      {/* Task Title */}
      <Text numberOfLines={1} style={styles.title}>
        {title}
      </Text>

      {/* Task Description (Single Line) */}
      <Text style={styles.description}>{note}</Text>

      {/* Footer Row: Avatars + Due Date */}

      <View style={styles.footerRow}>
        <View style={styles.avatarGroup}>
          {avatars?.map((avatar, index) => (
            <Image
              key={index}
              source={avatar ? {uri: avatar} : images.avatarPlaceholder}
              style={styles.avatar}
            />
          ))}
        </View>
        <Text style={styles.date}>
          {moment(dueDate.toDate()).format('ddd DD MMM YYYY')}
        </Text>
      </View>

      {/* Progress Bar + Percentage */}

      <View style={styles.progressRow}>
        <ProgressBar
          containerStyle={{width: '91%'}}
          max={tasks.length > 0 ? tasks.length : 1} // avoid divide by 0
          value={tasks.length > 0 ? completeTasks.length : 0} // show 0 progress if no tasks
          showBottomText={false}
          filledStyle={{height: 4}}
          unfilledStyle={{height: 4}}
        />
        <Text style={styles.percentText}>
          {' '}
          {tasks.length > 0
            ? `${Math.round((completeTasks.length / tasks.length) * 100)}%`
            : '0%'}
        </Text>
      </View>
    </TouchableOpacity>
  );
};

export default ManagerIssueCard;

// Styles
const styles = StyleSheet.create({
  card: {
    backgroundColor: Colors.white,
    borderRadius: 12,
    padding: 12,
  },
  labelsRow: {
    flexDirection: 'row',
    gap: 5,
    alignItems: 'center',
  },
  label: {
    paddingHorizontal: 8,
    paddingVertical: 3,
    borderRadius: 4,
  },
  labelText: {
    color: 'white',
    fontFamily: Fonts.Medium,
    fontSize: 9,
    textTransform: 'capitalize',
  },
  title: {
    fontSize: 14,
    fontFamily: Fonts.Semibold,
    color: '#080C10',
    marginTop: 8,
  },
  description: {
    color: '#4B5563',
    fontSize: 10,
    fontFamily: Fonts.Medium,
    paddingTop: 2,
  },
  date: {
    fontFamily: Fonts.Semibold,
    color: '#4B5563',
    fontSize: 10,
  },
  progressRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginTop: 13,
  },
  percentText: {
    color: Colors.primary,
    fontFamily: Fonts.Semibold,
    fontSize: 10,
  },
  footerRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 8,
    height: 34,
  },
  avatarGroup: {
    flexDirection: 'row',
  },
  avatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
    borderWidth: 2,
    borderColor: Colors.white,
    marginRight: -7,
  },
  detailsBtn: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 2,
  },
  detailsText: {
    color: '#0386AC',
    fontSize: 9,
    fontFamily: Fonts.Medium,
  },
});
